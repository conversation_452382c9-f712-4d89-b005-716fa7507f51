package com.ai.cutover.config;

import com.ai.cutover.common.entity.BaseEntity;
import com.mybatisflex.core.FlexGlobalConfig;
import com.mybatisflex.core.audit.AuditManager;
import com.mybatisflex.spring.boot.MyBatisFlexCustomizer;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDateTime;

/**
 * MyBatis-Flex配置类
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
@MapperScan("com.ai.cutover.**.dao")
public class MyBatisFlexConfig implements MyBatisFlexCustomizer {

	@Override
	public void customize(FlexGlobalConfig globalConfig) {
		// 开启SQL审计功能
		AuditManager.setAuditEnable(false);
		AuditManager.setMessageCollector(auditMessage -> {
			if (log.isDebugEnabled()) {
				log.debug("SQL执行: {} | 耗时: {}ms", auditMessage.getFullSql(), auditMessage.getElapsedTime());
			}
		});

		// 配置逻辑删除
		globalConfig.setNormalValueOfLogicDelete(0);
		globalConfig.setDeletedValueOfLogicDelete(1);

		// 配置自动填充
		globalConfig.registerInsertListener((entity) -> {
			if (entity instanceof BaseEntity baseEntity) {
				LocalDateTime now = LocalDateTime.now();
				if (baseEntity.getCreateTime() == null) {
					baseEntity.setCreateTime(now);
				}
				if (baseEntity.getUpdateTime() == null) {
					baseEntity.setUpdateTime(now);
				}
			}
		}, BaseEntity.class);

		globalConfig.registerUpdateListener((entity) -> {
			if (entity instanceof BaseEntity baseEntity) {
				baseEntity.setUpdateTime(LocalDateTime.now());
			}
		}, BaseEntity.class);
	}

}
