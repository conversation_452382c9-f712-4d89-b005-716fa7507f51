package com.ai.cutover.config;

import com.mybatisflex.core.FlexGlobalConfig;
import com.mybatisflex.core.audit.AuditManager;
import com.mybatisflex.spring.boot.MyBatisFlexCustomizer;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Configuration;

/**
 * MyBatis-Flex配置类
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
@MapperScan("com.ai.cutover.**.dao")
public class MyBatisFlexConfig implements MyBatisFlexCustomizer {

	@Override
	public void customize(FlexGlobalConfig globalConfig) {
		// 开启SQL审计功能
		AuditManager.setAuditEnable(false);
		AuditManager.setMessageCollector(auditMessage -> {
			if (log.isDebugEnabled()) {
				log.debug("SQL执行: {} | 耗时: {}ms", auditMessage.getFullSql(), auditMessage.getElapsedTime());
			}
		});

		// 配置逻辑删除
		globalConfig.setNormalValueOfLogicDelete(0);
		globalConfig.setDeletedValueOfLogicDelete(1);
	}

}
