package com.ai.cutover.config;

import cn.dev33.satoken.stp.StpUtil;
import com.ai.cutover.common.entity.BaseEntity;
import com.mybatisflex.core.FlexGlobalConfig;
import com.mybatisflex.core.audit.AuditManager;
import com.mybatisflex.spring.boot.MyBatisFlexCustomizer;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDateTime;

/**
 * MyBatis-Flex配置类
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
@MapperScan("com.ai.cutover.**.dao")
public class MyBatisFlexConfig implements MyBatisFlexCustomizer {

	@Override
	public void customize(FlexGlobalConfig globalConfig) {
		// 开启SQL审计功能
		AuditManager.setAuditEnable(false);
		AuditManager.setMessageCollector(auditMessage -> {
			if (log.isDebugEnabled()) {
				log.debug("SQL执行: {} | 耗时: {}ms", auditMessage.getFullSql(), auditMessage.getElapsedTime());
			}
		});

		// 配置逻辑删除
		globalConfig.setNormalValueOfLogicDelete(0);
		globalConfig.setDeletedValueOfLogicDelete(1);

		// 配置自动填充
		globalConfig.registerInsertListener((entity) -> {
			if (entity instanceof BaseEntity baseEntity) {
				LocalDateTime now = LocalDateTime.now();
				Long currentUserId = getCurrentUserId();

				// 自动填充创建时间
				if (baseEntity.getCreateTime() == null) {
					baseEntity.setCreateTime(now);
				}
				// 自动填充更新时间
				if (baseEntity.getUpdateTime() == null) {
					baseEntity.setUpdateTime(now);
				}
				// 自动填充创建人
				if (baseEntity.getCreateBy() == null && currentUserId != null) {
					baseEntity.setCreateBy(currentUserId);
				}
				// 自动填充更新人
				if (baseEntity.getUpdateBy() == null && currentUserId != null) {
					baseEntity.setUpdateBy(currentUserId);
				}
			}
		}, BaseEntity.class);

		globalConfig.registerUpdateListener((entity) -> {
			if (entity instanceof BaseEntity baseEntity) {
				LocalDateTime now = LocalDateTime.now();
				Long currentUserId = getCurrentUserId();

				// 自动填充更新时间
				baseEntity.setUpdateTime(now);
				// 自动填充更新人
				if (currentUserId != null) {
					baseEntity.setUpdateBy(currentUserId);
				}
			}
		}, BaseEntity.class);
	}

	/**
	 * 获取当前登录用户ID
	 * @return 用户ID，如果未登录则返回null
	 */
	private Long getCurrentUserId() {
		try {
			// 尝试获取当前登录用户ID
			Object loginId = StpUtil.getLoginIdDefaultNull();
			if (loginId != null) {
				return Long.valueOf(loginId.toString());
			}
		} catch (Exception e) {
			// 如果获取失败（比如未登录），记录日志但不抛出异常
			log.debug("获取当前用户ID失败，可能是系统内部操作或未登录状态: {}", e.getMessage());
		}
		return null;
	}

}
