package com.ai.cutover.module.process.model.entity;

import com.ai.cutover.common.entity.BaseEntity;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 流程部署历史实体类
 * 用于记录每次流程部署的详细信息和历史版本
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table("proc_deployment_history")
public class ProcessDeploymentHistory extends BaseEntity {

    /**
     * 流程定义ID
     */
    @NotNull(message = "流程定义ID不能为空")
    @Column("process_definition_id")
    private Long processDefinitionId;

    /**
     * 流程定义Key
     */
    @NotBlank(message = "流程定义Key不能为空")
    @Column("process_key")
    private String processKey;

    /**
     * 部署版本号
     */
    @NotNull(message = "版本号不能为空")
    @Column("version")
    private Integer version;

    /**
     * Camunda部署ID
     */
    @NotBlank(message = "Camunda部署ID不能为空")
    @Column("deployment_id")
    private String deploymentId;

    /**
     * Camunda流程定义ID
     */
    @NotBlank(message = "Camunda流程定义ID不能为空")
    @Column("camunda_process_definition_id")
    private String camundaProcessDefinitionId;

    /**
     * 部署名称
     */
    @NotBlank(message = "部署名称不能为空")
    @Column("deployment_name")
    private String deploymentName;

    /**
     * 部署时间
     */
    @Column("deployment_time")
    private LocalDateTime deploymentTime;

    /**
     * 部署状态（ACTIVE-激活，SUSPENDED-挂起，DELETED-已删除）
     */
    @Column("status")
    private String status;

    /**
     * 是否为当前版本
     */
    @Column("is_current")
    private Boolean isCurrentFlag;

    /**
     * 部署描述
     */
    @Column("description")
    private String description;

    /**
     * 部署原因
     */
    @Column("deploy_reason")
    private String deployReason;

}
