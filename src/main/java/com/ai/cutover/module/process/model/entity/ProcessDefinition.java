package com.ai.cutover.module.process.model.entity;

import com.ai.cutover.common.entity.BaseEntity;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;

/**
 * 流程定义信息实体类 用于存储自定义流程定义的元数据
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table("proc_definition")
public class ProcessDefinition extends BaseEntity {

	/**
	 * 流程定义Key（唯一标识）
	 */
	@NotBlank(message = "流程定义Key不能为空")
	@Column("process_key")
	private String processKey;

	/**
	 * 流程名称
	 */
	@NotBlank(message = "流程名称不能为空")
	@Column("process_name")
	private String processName;

	/**
	 * 流程描述
	 */
	@Column("description")
	private String description;

	/**
	 * 流程分类
	 */
	@Column("category")
	private String category;

	/**
	 * 流程版本
	 */
	@Column("version")
	private Integer version;

	/**
	 * BPMN XML内容
	 */
	@Column("bpmn_xml")
	private String bpmnXml;

	/**
	 * 流程图SVG内容
	 */
	@Column("diagram_svg")
	private String diagramSvg;

	/**
	 * 是否已部署
	 */
	@Column("is_deployed")
	private Boolean isDeployed;

	/**
	 * Camunda部署ID
	 */
	@Column("deployment_id")
	private String deploymentId;

	/**
	 * Camunda流程定义ID
	 */
	@Column("camunda_process_definition_id")
	private String camundaProcessDefinitionId;

	/**
	 * 流程状态（DRAFT-草稿，ACTIVE-激活，SUSPENDED-挂起）
	 */
	@Column("status")
	private String status;

	/**
	 * 设计器类型（BPMN_JS, CAMUNDA_MODELER等）
	 */
	@Column("designer_type")
	private String designerType;

	/**
	 * 设计器配置JSON
	 */
	@Column("designer_config")
	private String designerConfig;

	/**
	 * 流程标签（JSON数组） 用于多维度标记，如：["审批", "紧急", "常用", "模板"]
	 * 与category的区别：category是单一主分类，tags是多维度标签
	 */
	@Column("tags")
	private String tags;

	/**
	 * 备注
	 */
	@Column("remarks")
	private String remarks;

}
