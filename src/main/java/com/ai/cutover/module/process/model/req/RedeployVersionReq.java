package com.ai.cutover.module.process.model.req;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 重新部署指定版本请求
 *
 * <AUTHOR>
 */
@Data
public class RedeployVersionReq {

    /**
     * 流程定义Key
     */
    @NotBlank(message = "流程定义Key不能为空")
    private String processKey;

    /**
     * 要重新部署的版本号
     */
    @NotNull(message = "版本号不能为空")
    private Integer version;

}
